#!/usr/bin/env python3
"""
LLM不确定性分析系统配置文件
包含所有任务的配置设置
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union
from enum import Enum


class OutputFormat(Enum):
    """输出格式枚举"""
    CSV = "csv"
    JSON = "json"
    MONGODB = "mongodb"


class ModelProvider(Enum):
    """模型提供商枚举"""
    DASHSCOPE = "dashscope"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


@dataclass
class ModelConfig:
    """模型配置"""
    name: str = "qwen-32b"
    provider: ModelProvider = ModelProvider.DASHSCOPE
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    api_key_env: str = "DASHSCOPE_API_KEY"
    enable_thinking: bool = True
    enable_logprobs: bool = True
    top_logprobs: int = 5
    temperature: float = 0.7
    top_p: float = 0.95
    max_tokens: Optional[int] = None


@dataclass
class PromptConfig:
    """Prompt配置"""
    directory: str
    num_prompts: int = 10
    sample_count: int = 5  # 从num_prompts中随机选择的数量
    attempts_per_prompt: int = 8  # 每个prompt的重复次数
    template_variables: Dict[str, str] = field(default_factory=dict)


@dataclass
class DatasetConfig:
    """数据集配置"""
    name: str
    source_path: str
    sample_size: int = 500
    id_field: str = "id"
    text_field: str = "text"
    label_field: Optional[str] = None


@dataclass
class OutputConfig:
    """输出配置"""
    format: OutputFormat = OutputFormat.MONGODB
    # CSV/JSON输出配置
    output_dir: str = "output"
    filename_prefix: str = "llm_responses"
    # MongoDB输出配置
    mongo_host: str = "localhost"
    mongo_port: int = 27017
    mongo_database: str = "LLM-UQ"
    mongo_collection: str = "response"
    # 测试模式配置
    test_collection_suffix: str = "_test"


@dataclass
class TaskConfig:
    """单个任务配置"""
    name: str
    task_category: str
    dataset: DatasetConfig
    prompt: PromptConfig
    model: ModelConfig
    output: OutputConfig
    enabled: bool = True


@dataclass
class SystemConfig:
    """系统级配置"""
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "llm_response_generator.log"
    # 并发配置
    max_concurrent_requests: int = 10
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    # 进度恢复配置
    enable_resume: bool = True
    # 测试模式配置
    test_mode_sample_size: int = 1


class Config:
    """主配置类"""
    
    def __init__(self):
        self.system = SystemConfig()
        self.tasks = self._create_default_tasks()
    
    def _create_default_tasks(self) -> Dict[str, TaskConfig]:
        """创建默认任务配置"""
        tasks = {}
        
        # Twitter情感分析任务
        tasks["sentiment_analysis"] = TaskConfig(
            name="sentiment_analysis",
            task_category="sentiment_analysis",
            dataset=DatasetConfig(
                name="semeval2017",
                source_path="sampled_semeval.csv",
                sample_size=500,
                id_field="id",
                text_field="text",
                label_field="label"
            ),
            prompt=PromptConfig(
                directory="prompts/1_sentiment_analysis",
                num_prompts=10,
                sample_count=5,
                attempts_per_prompt=8,
                template_variables={"input_field": "tweet"}
            ),
            model=ModelConfig(
                name="qwen-32b",
                provider=ModelProvider.DASHSCOPE,
                enable_thinking=True,
                enable_logprobs=True
            ),
            output=OutputConfig(
                format=OutputFormat.MONGODB,
                mongo_collection="response"
            )
        )
        
        # PyTorch Commits探索性编码任务
        tasks["explorative_coding"] = TaskConfig(
            name="explorative_coding",
            task_category="open_explorative_coding",
            dataset=DatasetConfig(
                name="pytorch_commits",
                source_path="sampled_commits.csv",
                sample_size=500,
                id_field="sha",
                text_field="message",
                label_field=None
            ),
            prompt=PromptConfig(
                directory="prompts/2_explorative_coding_commits",
                num_prompts=10,
                sample_count=5,
                attempts_per_prompt=8,
                template_variables={
                    "repo_name": "pytorch/pytorch",
                    "message_field": "message"
                }
            ),
            model=ModelConfig(
                name="qwen-32b",
                provider=ModelProvider.DASHSCOPE,
                enable_thinking=True,
                enable_logprobs=True
            ),
            output=OutputConfig(
                format=OutputFormat.MONGODB,
                mongo_collection="response"
            )
        )
        
        return tasks
    
    def get_task(self, task_name: str) -> Optional[TaskConfig]:
        """获取指定任务配置"""
        return self.tasks.get(task_name)
    
    def get_enabled_tasks(self) -> Dict[str, TaskConfig]:
        """获取所有启用的任务配置"""
        return {name: task for name, task in self.tasks.items() if task.enabled}
    
    def update_task_config(self, task_name: str, **kwargs):
        """更新任务配置"""
        if task_name in self.tasks:
            task = self.tasks[task_name]
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
                else:
                    # 尝试更新嵌套配置
                    for attr_name in ['dataset', 'prompt', 'model', 'output']:
                        attr = getattr(task, attr_name)
                        if hasattr(attr, key):
                            setattr(attr, key, value)
                            break
    
    def set_test_mode(self, enabled: bool = True):
        """设置测试模式"""
        for task in self.tasks.values():
            if enabled:
                task.dataset.sample_size = self.system.test_mode_sample_size
                task.output.mongo_collection += self.output.test_collection_suffix
            else:
                # 恢复默认值需要重新创建配置
                pass
    
    def set_output_format(self, format_type: OutputFormat, **kwargs):
        """设置所有任务的输出格式"""
        for task in self.tasks.values():
            task.output.format = format_type
            for key, value in kwargs.items():
                if hasattr(task.output, key):
                    setattr(task.output, key, value)
    
    def set_model_config(self, **kwargs):
        """设置所有任务的模型配置"""
        for task in self.tasks.values():
            for key, value in kwargs.items():
                if hasattr(task.model, key):
                    setattr(task.model, key, value)
    
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        errors = []
        
        for task_name, task in self.tasks.items():
            # 检查数据集路径
            if not os.path.exists(task.dataset.source_path):
                errors.append(f"Task {task_name}: Dataset file not found: {task.dataset.source_path}")
            
            # 检查prompt目录
            if not os.path.exists(task.prompt.directory):
                errors.append(f"Task {task_name}: Prompt directory not found: {task.prompt.directory}")
            else:
                # 检查prompt文件
                for i in range(1, task.prompt.num_prompts + 1):
                    prompt_file = os.path.join(task.prompt.directory, f"prompt_{i:02d}.txt")
                    if not os.path.exists(prompt_file):
                        errors.append(f"Task {task_name}: Prompt file not found: {prompt_file}")
            
            # 检查API密钥
            api_key = os.getenv(task.model.api_key_env)
            if not api_key or api_key == 'null':
                errors.append(f"Task {task_name}: API key not set: {task.model.api_key_env}")
            
            # 检查采样配置
            if task.prompt.sample_count > task.prompt.num_prompts:
                errors.append(f"Task {task_name}: sample_count ({task.prompt.sample_count}) cannot be greater than num_prompts ({task.prompt.num_prompts})")
        
        return errors
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "system": self.system.__dict__,
            "tasks": {name: {
                "name": task.name,
                "task_category": task.task_category,
                "enabled": task.enabled,
                "dataset": task.dataset.__dict__,
                "prompt": task.prompt.__dict__,
                "model": task.model.__dict__,
                "output": task.output.__dict__
            } for name, task in self.tasks.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Config':
        """从字典创建配置"""
        config = cls()
        
        # 更新系统配置
        if "system" in data:
            for key, value in data["system"].items():
                if hasattr(config.system, key):
                    setattr(config.system, key, value)
        
        # 更新任务配置
        if "tasks" in data:
            for task_name, task_data in data["tasks"].items():
                if task_name in config.tasks:
                    task = config.tasks[task_name]
                    
                    # 更新基本属性
                    for attr in ["name", "task_category", "enabled"]:
                        if attr in task_data:
                            setattr(task, attr, task_data[attr])
                    
                    # 更新嵌套配置
                    for config_type in ["dataset", "prompt", "model", "output"]:
                        if config_type in task_data:
                            config_obj = getattr(task, config_type)
                            for key, value in task_data[config_type].items():
                                if hasattr(config_obj, key):
                                    setattr(config_obj, key, value)
        
        return config


# 创建全局配置实例
config = Config()
