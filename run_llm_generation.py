#!/usr/bin/env python3
"""
LLM响应生成运行脚本
支持分别运行数据采样和LLM响应生成
"""

import os
import sys
import argparse
from data_sampler import DataSampler
from llm_response_generator import LLMResponseGenerator

def check_requirements():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查API密钥（仅在运行LLM响应生成时需要）
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key or api_key == 'null':
        print("警告: DASHSCOPE_API_KEY 环境变量未设置")
        print("请设置您的API密钥: export DASHSCOPE_API_KEY='your-api-key'")
        return False
    
    # 检查数据文件
    if not os.path.exists('data/SemEval2017-task4-test.subtask-A.english.csv'):
        print("错误: SemEval数据文件不存在")
        return False
    
    if not os.path.exists('data/pytorch_commits/'):
        print("错误: <PERSON><PERSON><PERSON>orch commits目录不存在")
        return False
    
    # 检查prompt模板
    twitter_prompt_files = [
        'prompts/twitter_sentiment.txt',
        'prompts/twitter_sentiment_reason.txt',
        'prompts/twitter_sentiment_reason_first.txt'
    ]
    
    commit_prompt_files = [
        'prompts/commit_only_answer.txt',
        'prompts/commit_answer_then_reason.txt',
        'prompts/commit_reasoning_then_answer.txt'
    ]
    
    for file_path in twitter_prompt_files + commit_prompt_files:
        if not os.path.exists(file_path):
            print(f"警告: Prompt模板文件 {file_path} 不存在")
    
    print("环境检查完成")
    return True

def run_data_sampling(semeval_size=500, commits_size=500):
    """运行数据采样"""
    print("=" * 60)
    print("数据采样")
    print("=" * 60)
    
    try:
        sampler = DataSampler()
        semeval_data, commits_data = sampler.run_sampling(semeval_size, commits_size)
        
        print("\n" + "=" * 60)
        print("数据采样完成！")
        print("输出文件:")
        print("- sampled_semeval.csv: SemEval采样数据")
        print("- sampled_commits.csv: PyTorch commits采样数据")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n数据采样出错: {e}")
        return False

def run_llm_generation():
    """运行LLM响应生成"""
    print("=" * 60)
    print("LLM响应生成")
    print("=" * 60)
    
    # 检查CSV文件是否存在
    if not os.path.exists('sampled_semeval.csv') and not os.path.exists('sampled_commits.csv'):
        print("错误: 未找到采样数据CSV文件")
        print("请先运行数据采样: python run_llm_generation.py --sample")
        return False
    
    try:
        generator = LLMResponseGenerator()
        generator.run()
        
        print("\n" + "=" * 60)
        print("LLM响应生成完成！")
        print("输出:")
        print("- MongoDB LLM-UQ.response: 完整响应数据")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\nLLM响应生成出错: {e}")
        return False

def run_full_pipeline(semeval_size=500, commits_size=500):
    """运行完整流程"""
    print("=" * 60)
    print("完整LLM响应生成流程")
    print("=" * 60)
    
    # 1. 数据采样
    print("\n步骤1: 数据采样")
    if not run_data_sampling(semeval_size, commits_size):
        return False
    
    # 2. LLM响应生成
    print("\n步骤2: LLM响应生成")
    if not run_llm_generation():
        return False
    
    print("\n" + "=" * 60)
    print("完整流程执行成功！")
    print("=" * 60)
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LLM响应生成器')
    parser.add_argument('--sample', action='store_true', 
                       help='仅运行数据采样')
    parser.add_argument('--generate', action='store_true', 
                       help='仅运行LLM响应生成')
    parser.add_argument('--full', action='store_true', 
                       help='运行完整流程（默认）')
    parser.add_argument('--semeval-size', type=int, default=500,
                       help='SemEval采样数量（默认500）')
    parser.add_argument('--commits-size', type=int, default=500,
                       help='Commits采样数量（默认500）')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，默认运行完整流程
    if not any([args.sample, args.generate, args.full]):
        args.full = True
    
    try:
        if args.sample:
            # 仅运行数据采样
            success = run_data_sampling(args.semeval_size, args.commits_size)
        elif args.generate:
            # 仅运行LLM响应生成
            if not check_requirements():
                print("\n请解决上述问题后重新运行")
                sys.exit(1)
            success = run_llm_generation()
        else:
            # 运行完整流程
            if not check_requirements():
                print("\n请解决上述问题后重新运行")
                sys.exit(1)
            success = run_full_pipeline(args.semeval_size, args.commits_size)
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断了程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
