#!/usr/bin/env python3
"""
配置管理工具
提供配置的加载、保存、验证和命令行接口
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from config import Config, OutputFormat, ModelProvider


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.config = Config()
        
        if config_file and Path(config_file).exists():
            self.load_from_file(config_file)
    
    def load_from_file(self, file_path: str):
        """从文件加载配置"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Config file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.suffix.lower() == '.json':
                data = json.load(f)
            elif file_path.suffix.lower() in ['.yml', '.yaml']:
                data = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config file format: {file_path.suffix}")
        
        self.config = Config.from_dict(data)
        print(f"Loaded configuration from {file_path}")
    
    def save_to_file(self, file_path: str, format_type: str = "json"):
        """保存配置到文件"""
        file_path = Path(file_path)
        data = self.config.to_dict()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if format_type.lower() == 'json':
                json.dump(data, f, indent=2, ensure_ascii=False)
            elif format_type.lower() in ['yml', 'yaml']:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
        
        print(f"Saved configuration to {file_path}")
    
    def validate(self) -> bool:
        """验证配置"""
        errors = self.config.validate_config()
        
        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
        else:
            print("Configuration validation passed")
            return True
    
    def show_config(self, task_name: Optional[str] = None):
        """显示配置信息"""
        if task_name:
            task = self.config.get_task(task_name)
            if task:
                print(f"\n=== Task Configuration: {task_name} ===")
                self._print_task_config(task)
            else:
                print(f"Task not found: {task_name}")
        else:
            print("\n=== System Configuration ===")
            self._print_system_config()
            
            print("\n=== Task Configurations ===")
            for name, task in self.config.tasks.items():
                print(f"\n--- {name} ---")
                self._print_task_config(task)
    
    def _print_system_config(self):
        """打印系统配置"""
        system = self.config.system
        print(f"Log Level: {system.log_level}")
        print(f"Log File: {system.log_file}")
        print(f"Max Concurrent Requests: {system.max_concurrent_requests}")
        print(f"Enable Resume: {system.enable_resume}")
        print(f"Test Mode Sample Size: {system.test_mode_sample_size}")
    
    def _print_task_config(self, task):
        """打印任务配置"""
        print(f"  Enabled: {task.enabled}")
        print(f"  Category: {task.task_category}")
        
        print(f"  Dataset:")
        print(f"    Name: {task.dataset.name}")
        print(f"    Source: {task.dataset.source_path}")
        print(f"    Sample Size: {task.dataset.sample_size}")
        print(f"    ID Field: {task.dataset.id_field}")
        print(f"    Text Field: {task.dataset.text_field}")
        print(f"    Label Field: {task.dataset.label_field}")
        
        print(f"  Prompt:")
        print(f"    Directory: {task.prompt.directory}")
        print(f"    Num Prompts: {task.prompt.num_prompts}")
        print(f"    Sample Count: {task.prompt.sample_count}")
        print(f"    Attempts Per Prompt: {task.prompt.attempts_per_prompt}")
        print(f"    Template Variables: {task.prompt.template_variables}")
        
        print(f"  Model:")
        print(f"    Name: {task.model.name}")
        print(f"    Provider: {task.model.provider.value}")
        print(f"    Enable Thinking: {task.model.enable_thinking}")
        print(f"    Enable Logprobs: {task.model.enable_logprobs}")
        print(f"    Temperature: {task.model.temperature}")
        
        print(f"  Output:")
        print(f"    Format: {task.output.format.value}")
        if task.output.format == OutputFormat.MONGODB:
            print(f"    MongoDB Database: {task.output.mongo_database}")
            print(f"    MongoDB Collection: {task.output.mongo_collection}")
        else:
            print(f"    Output Directory: {task.output.output_dir}")
            print(f"    Filename Prefix: {task.output.filename_prefix}")
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        for key, value in updates.items():
            if '.' in key:
                # 支持嵌套键，如 "sentiment_analysis.model.temperature"
                parts = key.split('.')
                if len(parts) >= 2:
                    task_name = parts[0]
                    if task_name in self.config.tasks:
                        if len(parts) == 3:
                            # task.section.key
                            section_name, attr_name = parts[1], parts[2]
                            task = self.config.tasks[task_name]
                            if hasattr(task, section_name):
                                section = getattr(task, section_name)
                                if hasattr(section, attr_name):
                                    setattr(section, attr_name, value)
                                    print(f"Updated {key} = {value}")
                                else:
                                    print(f"Unknown attribute: {attr_name}")
                            else:
                                print(f"Unknown section: {section_name}")
                        elif len(parts) == 2:
                            # task.key
                            attr_name = parts[1]
                            task = self.config.tasks[task_name]
                            if hasattr(task, attr_name):
                                setattr(task, attr_name, value)
                                print(f"Updated {key} = {value}")
                            else:
                                print(f"Unknown attribute: {attr_name}")
                    else:
                        print(f"Unknown task: {task_name}")
            else:
                # 系统级配置
                if hasattr(self.config.system, key):
                    setattr(self.config.system, key, value)
                    print(f"Updated system.{key} = {value}")
                else:
                    print(f"Unknown system attribute: {key}")


def create_cli():
    """创建命令行接口"""
    parser = argparse.ArgumentParser(description="LLM不确定性分析系统配置管理工具")
    
    parser.add_argument('--config', '-c', type=str, 
                       help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # show命令
    show_parser = subparsers.add_parser('show', help='显示配置')
    show_parser.add_argument('--task', '-t', type=str, 
                           help='显示指定任务的配置')
    
    # validate命令
    validate_parser = subparsers.add_parser('validate', help='验证配置')
    
    # save命令
    save_parser = subparsers.add_parser('save', help='保存配置')
    save_parser.add_argument('file', type=str, help='输出文件路径')
    save_parser.add_argument('--format', '-f', choices=['json', 'yaml'], 
                           default='json', help='输出格式')
    
    # update命令
    update_parser = subparsers.add_parser('update', help='更新配置')
    update_parser.add_argument('updates', nargs='+', 
                             help='配置更新，格式：key=value')
    
    # create-template命令
    template_parser = subparsers.add_parser('create-template', help='创建配置模板')
    template_parser.add_argument('file', type=str, help='输出文件路径')
    template_parser.add_argument('--format', '-f', choices=['json', 'yaml'], 
                               default='json', help='输出格式')
    
    return parser


def main():
    """主函数"""
    parser = create_cli()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        manager = ConfigManager(args.config)
        
        if args.command == 'show':
            manager.show_config(args.task)
        
        elif args.command == 'validate':
            if manager.validate():
                print("✅ Configuration is valid")
                sys.exit(0)
            else:
                print("❌ Configuration has errors")
                sys.exit(1)
        
        elif args.command == 'save':
            manager.save_to_file(args.file, args.format)
        
        elif args.command == 'update':
            updates = {}
            for update in args.updates:
                if '=' in update:
                    key, value = update.split('=', 1)
                    # 尝试转换数据类型
                    try:
                        if value.lower() in ['true', 'false']:
                            value = value.lower() == 'true'
                        elif value.isdigit():
                            value = int(value)
                        elif '.' in value and value.replace('.', '').isdigit():
                            value = float(value)
                    except:
                        pass  # 保持字符串类型
                    updates[key] = value
                else:
                    print(f"Invalid update format: {update}")
                    continue
            
            manager.update_config(updates)
        
        elif args.command == 'create-template':
            manager.save_to_file(args.file, args.format)
            print(f"Created configuration template: {args.file}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
