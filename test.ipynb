{"cells": [{"cell_type": "code", "execution_count": null, "id": "564babf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chat response: ChatCompletion(id='chatcmpl-76fa9ec0da3e4252bbc15d25fd555162', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content=\"<think>\\nOkay, the user wants a short introduction to large language models. Let me start by recalling what I know about them. Large language models are AI systems trained on vast amounts of text data. They can understand and generate human-like text. But I need to make sure I cover the key points without getting too technical.\\n\\nFirst, I should mention their size—like the number of parameters, but maybe not get into the specifics. Then, their training data sources, which are diverse and extensive. Next, their capabilities: generating text, answering questions, coding, etc. Also, their applications in various fields. But I should keep it concise. Maybe mention that they're part of the transformer architecture? Wait, the user might not need the technical details. Maybe just say they use deep learning and neural networks. Also, note that they're used in chatbots, virtual assistants, content creation. Should I mention any specific examples like GPT or BERT? Probably not, since the user asked for a general intro. Need to keep it brief and avoid jargon. Let me structure it: definition, training process, capabilities, applications, and maybe a note on their impact. Let me check for clarity and flow. Make sure it's short, maybe 3-4 sentences. Avoid any markdown. Alright, that should cover it.\\n</think>\\n\\nLarge language models (LLMs) are advanced AI systems trained on vast amounts of text data to understand and generate human-like language. They use deep learning techniques to recognize patterns, enabling tasks like answering questions, creating content, coding, and engaging in conversations. These models, often based on transformer architectures, have revolutionized natural language processing by offering unprecedented versatility and scale, driving innovations in chatbots, virtual assistants, and more.\", refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=[], reasoning_content=None), stop_reason=None)], created=1754465560, model='Qwen/Qwen3-8B', object='chat.completion', service_tier=None, system_fingerprint=None, usage=CompletionUsage(completion_tokens=357, prompt_tokens=18, total_tokens=375, completion_tokens_details=None, prompt_tokens_details=None), prompt_logprobs=None, kv_transfer_params=None)\n"]}], "source": []}, {"cell_type": "code", "execution_count": 38, "id": "735cc43c", "metadata": {}, "outputs": [], "source": ["Tweet = \"I'm so happy today! But I'm so sad today!\"\n", "prompt = f'''\n", "Give the tweet’s sentiment a label from Positive, Negative, or Neutral.  Then provide your reasoning.\n", "Please strictly follow the format:\n", "[Label]: <label:Positive, Negative, Neutral>\n", "[Reasoning]: <reasoning>\n", "Here is the tweet:\n", "[Tweet]: {Tweet}\n", "'''\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "753de37b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Label]: Neutral  \n", "[Reasoning]: The tweet expresses both positive (\"so happy today\") and negative (\"so sad today\") emotions simultaneously, resulting in a balanced sentiment that neither strongly leans positive nor negative.\n"]}], "source": ["import os\n", "from openai import OpenAI\n", "\n", "# 建议将您的API Key配置到环境变量中，以降低泄露风险\n", "# 您也可以在代码中直接替换下面的 \"sk-xxx\"\n", "client = OpenAI(\n", "    api_key=os.getenv(\"DASHSCOPE_API_KEY\", 'null'),\n", "    base_url=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    # 请根据您的需求选择合适的qwen3模型名称\n", "    # 例如 qwen-3.5-7b-chat, qwen-3.5-32b-chat 等\n", "    model=\"qwen3-14b\", \n", "    messages=[\n", "        # {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"}, \n", "        {\"role\": \"user\", \"content\": prompt},\n", "    ],\n", "    logprobs=True,\n", "    # 通过 extra_body 参数关闭 thinking 模式\n", "    extra_body={\"enable_thinking\": False},\n", ")\n", "\n", "# 打印模型的回复\n", "if completion.choices:\n", "    print(completion.choices[0].message.content)\n", "else:\n", "    print(\"没有收到模型的有效回复。\")"]}, {"cell_type": "code", "execution_count": 40, "id": "012e88e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'content': [{'top_logprobs': [], 'logprob': 0.0, 'bytes': [91], 'token': '['},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [76, 97, 98, 101, 108],\n", "   'token': 'Label'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [93, 58], 'token': ']:'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 78, 101, 117, 116, 114, 97, 108],\n", "   'token': ' Neutral'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [32, 32, 10], 'token': '  \\n'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [91], 'token': '['},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [82, 101, 97, 115, 111, 110],\n", "   'token': 'Reason'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [105, 110, 103],\n", "   'token': 'ing'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [93, 58], 'token': ']:'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 84, 104, 101],\n", "   'token': ' The'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 116, 119, 101, 101, 116],\n", "   'token': ' tweet'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 101, 120, 112, 114, 101, 115, 115, 101, 115],\n", "   'token': ' expresses'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 98, 111, 116, 104],\n", "   'token': ' both'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 112, 111, 115, 105, 116, 105, 118, 101],\n", "   'token': ' positive'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [32, 40, 34], 'token': ' (\"'},\n", "  {'top_logprobs': [],\n", "   'logprob': -0.5302736759185791,\n", "   'bytes': [115, 111],\n", "   'token': 'so'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 104, 97, 112, 112, 121],\n", "   'token': ' happy'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 116, 111, 100, 97, 121],\n", "   'token': ' today'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [34, 41], 'token': '\")'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 97, 110, 100],\n", "   'token': ' and'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 110, 101, 103, 97, 116, 105, 118, 101],\n", "   'token': ' negative'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [32, 40, 34], 'token': ' (\"'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [115, 111], 'token': 'so'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 115, 97, 100],\n", "   'token': ' sad'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 116, 111, 100, 97, 121],\n", "   'token': ' today'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [34, 41], 'token': '\")'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 101, 109, 111, 116, 105, 111, 110, 115],\n", "   'token': ' emotions'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32,\n", "    115,\n", "    105,\n", "    109,\n", "    117,\n", "    108,\n", "    116,\n", "    97,\n", "    110,\n", "    101,\n", "    111,\n", "    117,\n", "    115,\n", "    108,\n", "    121],\n", "   'token': ' simultaneously'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [44], 'token': ','},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 114, 101, 115, 117, 108, 116, 105, 110, 103],\n", "   'token': ' resulting'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 105, 110],\n", "   'token': ' in'},\n", "  {'top_logprobs': [],\n", "   'logprob': -0.887814462184906,\n", "   'bytes': [32, 97],\n", "   'token': ' a'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 98, 97, 108, 97, 110, 99, 101, 100],\n", "   'token': ' balanced'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 115, 101, 110, 116, 105, 109, 101, 110, 116],\n", "   'token': ' sentiment'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 116, 104, 97, 116],\n", "   'token': ' that'},\n", "  {'top_logprobs': [],\n", "   'logprob': -0.5302721261978149,\n", "   'bytes': [32, 110, 101, 105, 116, 104, 101, 114],\n", "   'token': ' neither'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 115, 116, 114, 111, 110, 103, 108, 121],\n", "   'token': ' strongly'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 108, 101, 97, 110, 115],\n", "   'token': ' leans'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 112, 111, 115, 105, 116, 105, 118, 101],\n", "   'token': ' positive'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 110, 111, 114],\n", "   'token': ' nor'},\n", "  {'top_logprobs': [],\n", "   'logprob': 0.0,\n", "   'bytes': [32, 110, 101, 103, 97, 116, 105, 118, 101],\n", "   'token': ' negative'},\n", "  {'top_logprobs': [], 'logprob': 0.0, 'bytes': [46], 'token': '.'}]}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["completion.choices[0].message.logprobs"]}, {"cell_type": "code", "execution_count": 4, "id": "0a1e0d6d", "metadata": {}, "outputs": [{"ename": "APIConnectionError", "evalue": "Connection error.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_transports/default.py:101\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    100\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    102\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_transports/default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_sync/connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_sync/connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    237\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\n\u001b[32m    238\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_sync/connection.py:101\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m._connect_failed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m    103\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection.handle_request(request)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_sync/connection.py:78\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m     77\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m78\u001b[39m     stream = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     80\u001b[39m     ssl_object = stream.get_extra_info(\u001b[33m\"\u001b[39m\u001b[33mssl_object\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_sync/connection.py:124\u001b[39m, in \u001b[36mHTTPConnection._connect\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    123\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mconnect_tcp\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m--> \u001b[39m\u001b[32m124\u001b[39m     stream = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_network_backend\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect_tcp\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    125\u001b[39m     trace.return_value = stream\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_backends/sync.py:207\u001b[39m, in \u001b[36mSyncBackend.connect_tcp\u001b[39m\u001b[34m(self, host, port, timeout, local_address, socket_options)\u001b[39m\n\u001b[32m    202\u001b[39m exc_map: ExceptionMapping = {\n\u001b[32m    203\u001b[39m     socket.timeout: ConnectTimeout,\n\u001b[32m    204\u001b[39m     \u001b[38;5;167;01mOSError\u001b[39;00m: ConnectError,\n\u001b[32m    205\u001b[39m }\n\u001b[32m--> \u001b[39m\u001b[32m207\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    208\u001b[39m     sock = socket.create_connection(\n\u001b[32m    209\u001b[39m         address,\n\u001b[32m    210\u001b[39m         timeout,\n\u001b[32m    211\u001b[39m         source_address=source_address,\n\u001b[32m    212\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/miniconda3/lib/python3.12/contextlib.py:158\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    157\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m158\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    160\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    161\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    162\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpcore/_exceptions.py:14\u001b[39m, in \u001b[36mmap_exceptions\u001b[39m\u001b[34m(map)\u001b[39m\n\u001b[32m     13\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(exc, from_exc):\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m to_exc(exc) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 111] Connection refused", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/openai/_base_client.py:972\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m    971\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m972\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_client\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    973\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    974\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_should_stream_response_body\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    975\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    976\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    977\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m httpx.TimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_transports/default.py:249\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m    250\u001b[39m     resp = \u001b[38;5;28mself\u001b[39m._pool.handle_request(req)\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/miniconda3/lib/python3.12/contextlib.py:158\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    157\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m158\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    160\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    161\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    162\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/httpx/_transports/default.py:118\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    117\u001b[39m message = \u001b[38;5;28mstr\u001b[39m(exc)\n\u001b[32m--> \u001b[39m\u001b[32m118\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m mapped_exc(message) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 111] Connection refused", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mAPIConnectionError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      4\u001b[39m openai_api_base = \u001b[33m\"\u001b[39m\u001b[33mhttp://localhost:8000/v1\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      6\u001b[39m client = OpenAI(\n\u001b[32m      7\u001b[39m     api_key=openai_api_key,\n\u001b[32m      8\u001b[39m     base_url=openai_api_base,\n\u001b[32m      9\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m chat_response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mQwen/Qwen3-14B-FP8\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\n\u001b[32m     14\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m<PERSON>e\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontent\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGive me a short introduction to large language models.\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[43m    \u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m8192\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.7\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.8\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     19\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1.5\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     20\u001b[39m \u001b[43m    \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m     21\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_k\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m20\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[32m     22\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mchat_template_kwargs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43menable_thinking\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     23\u001b[39m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     24\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     25\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mChat response:\u001b[39m\u001b[33m\"\u001b[39m, chat_response)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/openai/_utils/_utils.py:287\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    285\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    286\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m287\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/openai/resources/chat/completions/completions.py:925\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    882\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m    883\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m    884\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    922\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m    923\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m    924\u001b[39m     validate_response_format(response_format)\n\u001b[32m--> \u001b[39m\u001b[32m925\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    926\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    927\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    928\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    929\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    930\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    931\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    932\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    933\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    934\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    935\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    936\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    937\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    938\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    939\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    940\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    941\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    942\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    947\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    948\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    949\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    950\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    951\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    952\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    953\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    954\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    955\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    956\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    957\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    958\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    959\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mweb_search_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mweb_search_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    960\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    961\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParamsStreaming\u001b[49m\n\u001b[32m    962\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\n\u001b[32m    963\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParamsNonStreaming\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    964\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    965\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    966\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    967\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    968\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    969\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    970\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    971\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/openai/_base_client.py:1249\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1235\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1236\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1237\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1244\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1245\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1246\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1247\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1248\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1249\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/openai/_base_client.py:1004\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1001\u001b[39m         \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m   1003\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRaising connection error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1004\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(request=request) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   1006\u001b[39m log.debug(\n\u001b[32m   1007\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mHTTP Response: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%i\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m'\u001b[39m,\n\u001b[32m   1008\u001b[39m     request.method,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1012\u001b[39m     response.headers,\n\u001b[32m   1013\u001b[39m )\n\u001b[32m   1014\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mrequest_id: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, response.headers.get(\u001b[33m\"\u001b[39m\u001b[33mx-request-id\u001b[39m\u001b[33m\"\u001b[39m))\n", "\u001b[31mAPIConnectionError\u001b[39m: Connection error."]}], "source": ["from openai import OpenAI\n", "# Set OpenAI's API key and API base to use vLLM's API server.\n", "openai_api_key = \"EMPTY\"\n", "openai_api_base = \"http://localhost:8000/v1\"\n", "\n", "client = OpenAI(\n", "    api_key=openai_api_key,\n", "    base_url=openai_api_base,\n", ")\n", "\n", "chat_response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen3-14B-FP8\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Give me a short introduction to large language models.\"},\n", "    ],\n", "    max_tokens=8192,\n", "    temperature=0.7,\n", "    top_p=0.8,\n", "    presence_penalty=1.5,\n", "    extra_body={\n", "        \"top_k\": 20, \n", "        \"chat_template_kwargs\": {\"enable_thinking\": False},\n", "    },\n", ")\n", "print(\"Chat response:\", chat_response)"]}, {"cell_type": "code", "execution_count": 3, "id": "49a3d50f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO 08-07 11:24:44 [config.py:1604] Using max model len 40960\n", "INFO 08-07 11:24:44 [config.py:2434] Chunked prefill is enabled with max_num_batched_tokens=8192.\n", "INFO 08-07 11:24:46 [core.py:572] Waiting for init message from front-end.\n", "INFO 08-07 11:24:46 [core.py:71] Initializing a V1 LLM engine (v0.10.0) with config: model='Qwen/Qwen3-32B', speculative_config=None, tokenizer='Qwen/Qwen3-32B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config={}, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(backend='auto', disable_fallback=False, disable_any_whitespace=False, disable_additional_properties=False, reasoning_backend=''), observability_config=ObservabilityConfig(show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None), seed=0, served_model_name=Qwen/Qwen3-32B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, pooler_config=None, compilation_config={\"level\":3,\"debug_dump_path\":\"\",\"cache_dir\":\"\",\"backend\":\"\",\"custom_ops\":[],\"splitting_ops\":[\"vllm.unified_attention\",\"vllm.unified_attention_with_output\",\"vllm.mamba_mixer2\"],\"use_inductor\":true,\"compile_sizes\":[],\"inductor_compile_config\":{\"enable_auto_functionalized_v2\":false},\"inductor_passes\":{},\"use_cudagraph\":true,\"cudagraph_num_of_warmups\":1,\"cudagraph_capture_sizes\":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"cudagraph_copy_inputs\":false,\"full_cuda_graph\":false,\"max_capture_size\":512,\"local_cache_dir\":null}\n", "INFO 08-07 11:24:47 [parallel_state.py:1102] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0, EP rank 0\n", "WARNING 08-07 11:24:47 [topk_topp_sampler.py:59] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.\n", "INFO 08-07 11:24:47 [gpu_model_runner.py:1843] Starting to load model Qwen/Qwen3-32B...\n", "INFO 08-07 11:24:47 [gpu_model_runner.py:1875] Loading model from scratch...\n", "INFO 08-07 11:24:47 [cuda.py:290] Using Flash Attention backend on V1 engine.\n", "ERROR 08-07 11:24:48 [core.py:632] EngineCore failed to start.\n", "ERROR 08-07 11:24:48 [core.py:632] Traceback (most recent call last):\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 623, in run_engine_core\n", "ERROR 08-07 11:24:48 [core.py:632]     engine_core = EngineCoreProc(*args, **kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 441, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     super().__init__(vllm_config, executor_class, log_stats,\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 77, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.model_executor = executor_class(vllm_config)\n", "ERROR 08-07 11:24:48 [core.py:632]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/executor_base.py\", line 53, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self._init_executor()\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py\", line 49, in _init_executor\n", "ERROR 08-07 11:24:48 [core.py:632]     self.collective_rpc(\"load_model\")\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py\", line 58, in collective_rpc\n", "ERROR 08-07 11:24:48 [core.py:632]     answer = run_method(self.driver_worker, method, args, kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/utils/__init__.py\", line 2985, in run_method\n", "ERROR 08-07 11:24:48 [core.py:632]     return func(*args, **kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]            ^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py\", line 201, in load_model\n", "ERROR 08-07 11:24:48 [core.py:632]     self.model_runner.load_model(eep_scale_up=eep_scale_up)\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py\", line 1876, in load_model\n", "ERROR 08-07 11:24:48 [core.py:632]     self.model = model_loader.load_model(\n", "ERROR 08-07 11:24:48 [core.py:632]                  ^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/model_loader/base_loader.py\", line 44, in load_model\n", "ERROR 08-07 11:24:48 [core.py:632]     model = initialize_model(vllm_config=vllm_config,\n", "ERROR 08-07 11:24:48 [core.py:632]             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/model_loader/utils.py\", line 67, in initialize_model\n", "ERROR 08-07 11:24:48 [core.py:632]     return model_class(vllm_config=vllm_config, prefix=prefix)\n", "ERROR 08-07 11:24:48 [core.py:632]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 271, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.model = Qwen3Model(vllm_config=vllm_config,\n", "ERROR 08-07 11:24:48 [core.py:632]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/compilation/decorators.py\", line 183, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 243, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     super().__init__(vllm_config=vllm_config,\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/compilation/decorators.py\", line 183, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 316, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.start_layer, self.end_layer, self.layers = make_layers(\n", "ERROR 08-07 11:24:48 [core.py:632]                                                     ^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/utils.py\", line 640, in make_layers\n", "ERROR 08-07 11:24:48 [core.py:632]     maybe_offload_to_cpu(layer_fn(prefix=f\"{prefix}.{idx}\"))\n", "ERROR 08-07 11:24:48 [core.py:632]                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 318, in <lambda>\n", "ERROR 08-07 11:24:48 [core.py:632]     lambda prefix: decoder_layer_type(config=config,\n", "ERROR 08-07 11:24:48 [core.py:632]                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 189, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.mlp = Qwen3MLP(\n", "ERROR 08-07 11:24:48 [core.py:632]                ^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 71, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.gate_up_proj = MergedColumnParallelLinear(\n", "ERROR 08-07 11:24:48 [core.py:632]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 651, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     super().__init__(input_size=input_size,\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 510, in __init__\n", "ERROR 08-07 11:24:48 [core.py:632]     self.quant_method.create_weights(\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 192, in create_weights\n", "ERROR 08-07 11:24:48 [core.py:632]     weight = Parameter(torch.empty(sum(output_partition_sizes),\n", "ERROR 08-07 11:24:48 [core.py:632]                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632]   File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/torch/utils/_device.py\", line 104, in __torch_function__\n", "ERROR 08-07 11:24:48 [core.py:632]     return func(*args, **kwargs)\n", "ERROR 08-07 11:24:48 [core.py:632]            ^^^^^^^^^^^^^^^^^^^^^\n", "ERROR 08-07 11:24:48 [core.py:632] torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 500.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 265.56 MiB is free. Process 3236 has 352.70 MiB memory in use. Including non-PyTorch memory, this process has 23.02 GiB memory in use. Of the allocated memory 22.60 GiB is allocated by PyTorch, and 18.72 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Process EngineCore_0:\n", "Traceback (most recent call last):\n", "  File \"/opt/miniconda3/lib/python3.12/multiprocessing/process.py\", line 314, in _bootstrap\n", "    self.run()\n", "  File \"/opt/miniconda3/lib/python3.12/multiprocessing/process.py\", line 108, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 636, in run_engine_core\n", "    raise e\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 623, in run_engine_core\n", "    engine_core = EngineCoreProc(*args, **kwargs)\n", "                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 441, in __init__\n", "    super().__init__(vllm_config, executor_class, log_stats,\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core.py\", line 77, in __init__\n", "    self.model_executor = executor_class(vllm_config)\n", "                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/executor_base.py\", line 53, in __init__\n", "    self._init_executor()\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py\", line 49, in _init_executor\n", "    self.collective_rpc(\"load_model\")\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py\", line 58, in collective_rpc\n", "    answer = run_method(self.driver_worker, method, args, kwargs)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/utils/__init__.py\", line 2985, in run_method\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py\", line 201, in load_model\n", "    self.model_runner.load_model(eep_scale_up=eep_scale_up)\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py\", line 1876, in load_model\n", "    self.model = model_loader.load_model(\n", "                 ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/model_loader/base_loader.py\", line 44, in load_model\n", "    model = initialize_model(vllm_config=vllm_config,\n", "            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/model_loader/utils.py\", line 67, in initialize_model\n", "    return model_class(vllm_config=vllm_config, prefix=prefix)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 271, in __init__\n", "    self.model = Qwen3Model(vllm_config=vllm_config,\n", "                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/compilation/decorators.py\", line 183, in __init__\n", "    old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 243, in __init__\n", "    super().__init__(vllm_config=vllm_config,\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/compilation/decorators.py\", line 183, in __init__\n", "    old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 316, in __init__\n", "    self.start_layer, self.end_layer, self.layers = make_layers(\n", "                                                    ^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/utils.py\", line 640, in make_layers\n", "    maybe_offload_to_cpu(layer_fn(prefix=f\"{prefix}.{idx}\"))\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 318, in <lambda>\n", "    lambda prefix: decoder_layer_type(config=config,\n", "                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py\", line 189, in __init__\n", "    self.mlp = Qwen3MLP(\n", "               ^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py\", line 71, in __init__\n", "    self.gate_up_proj = MergedColumnParallelLinear(\n", "                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 651, in __init__\n", "    super().__init__(input_size=input_size,\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 510, in __init__\n", "    self.quant_method.create_weights(\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py\", line 192, in create_weights\n", "    weight = Parameter(torch.empty(sum(output_partition_sizes),\n", "                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/torch/utils/_device.py\", line 104, in __torch_function__\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 500.00 MiB. GPU 0 has a total capacity of 23.64 GiB of which 265.56 MiB is free. Process 3236 has 352.70 MiB memory in use. Including non-PyTorch memory, this process has 23.02 GiB memory in use. Of the allocated memory 22.60 GiB is allocated by PyTorch, and 18.72 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)\n"]}, {"ename": "RuntimeError", "evalue": "Engine core initialization failed. See root cause above. Failed core proc(s): {}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      8\u001b[39m sampling_params = SamplingParams(temperature=\u001b[32m0.6\u001b[39m, top_p=\u001b[32m0.95\u001b[39m, top_k=\u001b[32m20\u001b[39m, max_tokens=\u001b[32m32768\u001b[39m)\n\u001b[32m     10\u001b[39m \u001b[38;5;66;03m# Initialize the vLLM engine\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m llm = \u001b[43mLLM\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mQwen/Qwen3-32B\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Prepare the input to the model\u001b[39;00m\n\u001b[32m     14\u001b[39m prompt = \u001b[33m\"\u001b[39m\u001b[33mGive me a short introduction to large language models.\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/entrypoints/llm.py:273\u001b[39m, in \u001b[36mLLM.__init__\u001b[39m\u001b[34m(self, model, task, tokenizer, tokenizer_mode, skip_tokenizer_init, trust_remote_code, allowed_local_media_path, tensor_parallel_size, dtype, quantization, revision, tokenizer_revision, seed, gpu_memory_utilization, swap_space, cpu_offload_gb, enforce_eager, max_seq_len_to_capture, disable_custom_all_reduce, disable_async_output_proc, hf_token, hf_overrides, mm_processor_kwargs, override_pooler_config, compilation_config, **kwargs)\u001b[39m\n\u001b[32m    243\u001b[39m engine_args = EngineArgs(\n\u001b[32m    244\u001b[39m     model=model,\n\u001b[32m    245\u001b[39m     task=task,\n\u001b[32m   (...)\u001b[39m\u001b[32m    269\u001b[39m     **kwargs,\n\u001b[32m    270\u001b[39m )\n\u001b[32m    272\u001b[39m \u001b[38;5;66;03m# Create the Engine (autoselects V0 vs V1)\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m273\u001b[39m \u001b[38;5;28mself\u001b[39m.llm_engine = \u001b[43mLLMEngine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_engine_args\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    274\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine_args\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m=\u001b[49m\u001b[43mUsageContext\u001b[49m\u001b[43m.\u001b[49m\u001b[43mLLM_CLASS\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    275\u001b[39m \u001b[38;5;28mself\u001b[39m.engine_class = \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m.llm_engine)\n\u001b[32m    277\u001b[39m \u001b[38;5;28mself\u001b[39m.request_counter = Counter()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/engine/llm_engine.py:497\u001b[39m, in \u001b[36mLLMEngine.from_engine_args\u001b[39m\u001b[34m(cls, engine_args, usage_context, stat_loggers)\u001b[39m\n\u001b[32m    494\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mvllm\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv1\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mengine\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mllm_engine\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m LLMEngine \u001b[38;5;28;01mas\u001b[39;00m V1LLMEngine\n\u001b[32m    495\u001b[39m     engine_cls = V1LLMEngine\n\u001b[32m--> \u001b[39m\u001b[32m497\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mengine_cls\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_vllm_config\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    498\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    499\u001b[39m \u001b[43m    \u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m=\u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    500\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstat_loggers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstat_loggers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    501\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdisable_log_stats\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_args\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdisable_log_stats\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    502\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/llm_engine.py:126\u001b[39m, in \u001b[36mLLMEngine.from_vllm_config\u001b[39m\u001b[34m(cls, vllm_config, usage_context, stat_loggers, disable_log_stats)\u001b[39m\n\u001b[32m    118\u001b[39m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[32m    119\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mfrom_vllm_config\u001b[39m(\n\u001b[32m    120\u001b[39m     \u001b[38;5;28mcls\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    124\u001b[39m     disable_log_stats: \u001b[38;5;28mbool\u001b[39m = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    125\u001b[39m ) -> \u001b[33m\"\u001b[39m\u001b[33mLLMEngine\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m126\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    127\u001b[39m \u001b[43m               \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m=\u001b[49m\u001b[43mExecutor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_class\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    128\u001b[39m \u001b[43m               \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m=\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mdisable_log_stats\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    129\u001b[39m \u001b[43m               \u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m=\u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    130\u001b[39m \u001b[43m               \u001b[49m\u001b[43mstat_loggers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstat_loggers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    131\u001b[39m \u001b[43m               \u001b[49m\u001b[43mmultiprocess_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43menvs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mVLLM_ENABLE_V1_MULTIPROCESSING\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/llm_engine.py:103\u001b[39m, in \u001b[36mLLMEngine.__init__\u001b[39m\u001b[34m(self, vllm_config, executor_class, log_stats, usage_context, stat_loggers, mm_registry, use_cached_outputs, multiprocess_mode)\u001b[39m\n\u001b[32m     99\u001b[39m \u001b[38;5;28mself\u001b[39m.output_processor = OutputProcessor(\u001b[38;5;28mself\u001b[39m.tokenizer,\n\u001b[32m    100\u001b[39m                                         log_stats=\u001b[38;5;28mself\u001b[39m.log_stats)\n\u001b[32m    102\u001b[39m \u001b[38;5;66;03m# EngineCore (gets EngineCoreRequests and gives EngineCoreOutputs)\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m103\u001b[39m \u001b[38;5;28mself\u001b[39m.engine_core = \u001b[43mEngineCoreClient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmake_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    104\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmultiprocess_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmultiprocess_mode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    105\u001b[39m \u001b[43m    \u001b[49m\u001b[43masyncio_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    106\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    107\u001b[39m \u001b[43m    \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    108\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    109\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m multiprocess_mode:\n\u001b[32m    112\u001b[39m     \u001b[38;5;66;03m# for v0 compatibility\u001b[39;00m\n\u001b[32m    113\u001b[39m     \u001b[38;5;28mself\u001b[39m.model_executor = \u001b[38;5;28mself\u001b[39m.engine_core.engine_core.model_executor  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core_client.py:77\u001b[39m, in \u001b[36mEngineCoreClient.make_client\u001b[39m\u001b[34m(multiprocess_mode, asyncio_mode, vllm_config, executor_class, log_stats)\u001b[39m\n\u001b[32m     73\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m EngineCoreClient.make_async_mp_client(\n\u001b[32m     74\u001b[39m         vllm_config, executor_class, log_stats)\n\u001b[32m     76\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m multiprocess_mode \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m asyncio_mode:\n\u001b[32m---> \u001b[39m\u001b[32m77\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mSyncMPClient\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m InprocClient(vllm_config, executor_class, log_stats)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core_client.py:514\u001b[39m, in \u001b[36mSyncMPClient.__init__\u001b[39m\u001b[34m(self, vllm_config, executor_class, log_stats)\u001b[39m\n\u001b[32m    512\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, vllm_config: VllmConfig, executor_class: \u001b[38;5;28mtype\u001b[39m[Executor],\n\u001b[32m    513\u001b[39m              log_stats: \u001b[38;5;28mbool\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m514\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    515\u001b[39m \u001b[43m        \u001b[49m\u001b[43masyncio_mode\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mF<PERSON>e\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    516\u001b[39m \u001b[43m        \u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    517\u001b[39m \u001b[43m        \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    518\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    519\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    521\u001b[39m     \u001b[38;5;28mself\u001b[39m.is_dp = \u001b[38;5;28mself\u001b[39m.vllm_config.parallel_config.data_parallel_size > \u001b[32m1\u001b[39m\n\u001b[32m    522\u001b[39m     \u001b[38;5;28mself\u001b[39m.outputs_queue = queue.Queue[Union[EngineCoreOutputs, \u001b[38;5;167;01mException\u001b[39;00m]]()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/core_client.py:408\u001b[39m, in \u001b[36mMPClient.__init__\u001b[39m\u001b[34m(self, asyncio_mode, vllm_config, executor_class, log_stats, client_addresses)\u001b[39m\n\u001b[32m    404\u001b[39m     \u001b[38;5;28mself\u001b[39m.stats_update_address = client_addresses.get(\n\u001b[32m    405\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mstats_update_address\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    406\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    407\u001b[39m     \u001b[38;5;66;03m# Engines are managed by this client.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m408\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mlaunch_core_engines\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutor_class\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    409\u001b[39m \u001b[43m                             \u001b[49m\u001b[43mlog_stats\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mas\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mengine_manager\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    410\u001b[39m \u001b[43m                                            \u001b[49m\u001b[43mcoordinator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    411\u001b[39m \u001b[43m                                            \u001b[49m\u001b[43maddresses\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    412\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mresources\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcoordinator\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mcoordinator\u001b[49m\n\u001b[32m    413\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mresources\u001b[49m\u001b[43m.\u001b[49m\u001b[43mengine_manager\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mengine_manager\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/miniconda3/lib/python3.12/contextlib.py:144\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    142\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m typ \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[32m    143\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m144\u001b[39m         \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    145\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n\u001b[32m    146\u001b[39m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/utils.py:697\u001b[39m, in \u001b[36mlaunch_core_engines\u001b[39m\u001b[34m(vllm_config, executor_class, log_stats, num_api_servers)\u001b[39m\n\u001b[32m    694\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m local_engine_manager, coordinator, addresses\n\u001b[32m    696\u001b[39m \u001b[38;5;66;03m# Now wait for engines to start.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m697\u001b[39m \u001b[43mwait_for_engine_startup\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    698\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhandshake_socket\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    699\u001b[39m \u001b[43m    \u001b[49m\u001b[43maddresses\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    700\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengines_to_handshake\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    701\u001b[39m \u001b[43m    \u001b[49m\u001b[43mparallel_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    702\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvllm_config\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcache_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    703\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlocal_engine_manager\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    704\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcoordinator\u001b[49m\u001b[43m.\u001b[49m\u001b[43mproc\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcoordinator\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    705\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/repo/llm-uncertainty-1/.venv/lib/python3.12/site-packages/vllm/v1/engine/utils.py:750\u001b[39m, in \u001b[36mwait_for_engine_startup\u001b[39m\u001b[34m(handshake_socket, addresses, core_engines, parallel_config, cache_config, proc_manager, coord_process)\u001b[39m\n\u001b[32m    748\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m coord_process \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m coord_process.exitcode \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    749\u001b[39m         finished[coord_process.name] = coord_process.exitcode\n\u001b[32m--> \u001b[39m\u001b[32m750\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mEngine core initialization failed. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    751\u001b[39m                        \u001b[33m\"\u001b[39m\u001b[33mSee root cause above. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    752\u001b[39m                        \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFailed core proc(s): \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfinished\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    754\u001b[39m \u001b[38;5;66;03m# Receive HELLO and READY messages from the input socket.\u001b[39;00m\n\u001b[32m    755\u001b[39m eng_identity, ready_msg_bytes = handshake_socket.recv_multipart()\n", "\u001b[31mRuntimeError\u001b[39m: Engine core initialization failed. See root cause above. Failed core proc(s): {}"]}], "source": ["from transformers import AutoTokenizer\n", "from vllm import LLM, SamplingParams\n", "\n", "# Initialize the tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen3-8B\")\n", "\n", "# Configurae the sampling parameters (for thinking mode)\n", "sampling_params = SamplingParams(temperature=0.6, top_p=0.95, top_k=20, max_tokens=32768)\n", "\n", "# Initialize the vLLM engine\n", "llm = LLM(model=\"Qwen/Qwen3-32B\")\n", "\n", "# Prepare the input to the model\n", "prompt = \"Give me a short introduction to large language models.\"\n", "messages = [\n", "    {\"role\": \"user\", \"content\": prompt}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=False,\n", "    add_generation_prompt=True,\n", "    enable_thinking=True,  # Set to False to strictly disable thinking\n", ")\n", "\n", "# Generate outputs\n", "outputs = llm.generate([text], sampling_params)\n", "\n", "# Print the outputs.\n", "for output in outputs:\n", "    prompt = output.prompt\n", "    generated_text = output.outputs[0].text\n", "    print(f\"Prompt: {prompt!r}, Generated text: {generated_text!r}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "e8b05fe9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'output': None,\n", " 'logprobs': None,\n", " 'status_code': 400,\n", " 'message': 'parameter.enable_thinking must be set to false for non-streaming calls'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}